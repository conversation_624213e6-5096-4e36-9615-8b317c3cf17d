在当前目录作为项目根目录，写一个Visual Studio 2022项目的视窗exe软件用于使用aspose功能对word批量自动排版，aspose版本为25.4.0.0.，功能实现参考aspose官方api文档，aspose使用nuget版本，许可证在项目里Aspose.Total.NET.lic。.net版本要求为6.0.相关功能要求如下
一、软件布局：软件由上而下分为若干区域
1、路径区域：来源目录（可选是否处理子目录）、目标区域（处理后的文件到达复制到的位置，可选是否保持原目录结构）、移动文件时冲突处理选项。
2、页面设置区域：①页面布局：上、下、左、右边距，单位cm；纸张方向可选纵向、横向。②纸张大小：默认、也可以自定义宽度和高度单位厘米。
3、文件内容替换区域（弹出框设置，用户可以一个个添加内容替换规则，也可以批量导入内容替换规则，用于对文档内容进行批量替换）、手动换行符替换为段落换行符（可选功能）

4、全文设置区域：
（1）段落格式：
设置段落的对齐方式：（左对齐、居中对齐、右对齐、两端对齐、分散对齐）单选，默认左对齐；
大纲级别（正文文本、1级、2级、3级、4级、5级、6级、7级、8级、）单选，默认正文文本；
对齐方向：（从右向左、从左向右）单选，默认从左向右；
缩进：文本之前XX字符（默认0字符），文本之后XX字符（默认0字符）。特殊格式：无、首行缩进xx字符（默认2字符）、悬挂缩进xx字符（默认2字符 ）， 单选。
间距：行间距的段前距离XX行（默认数值0），段后距离XX行（默认数值0）。行距可选单倍行距、1.5被行距、2被行距、固定值XX磅，单选，默认为1.5被行距。
（2）字体格式：
中文字体类型、中文字体字形（常规、倾斜、加粗、加粗倾斜）单选 默认常规、中文字体字号XX磅，默认12磅。
西文字体类型、西文字体字形（常规、倾斜、加粗、加粗倾斜）单选、西文字体字号XX磅，默认12磅。
复杂文种字体类型、复杂文种字体字形（常规、倾斜、加粗、加粗倾斜）单选、复杂文种字体字号XX磅，默认12磅。
文字字体颜色（颜色板选择），默认黑色。
注：XXX为用户手动输入内容，此段设置和字体设置是对全文所有内容生效。

5、段落匹配设置区域：
文章大标题、文章小标题、一级标题、二级标题、三级标题、四级标题、五级标题、六级标题、七级标题
以上选项有每个弹出框让用户设定匹配规则和设定对应段落和字体格式。上方功能按钮每行三个，共三行，排列整齐，按钮文字在按钮区域内水平垂直居中。
弹出框内容：
（1）段落匹配开头、此段包含关键词、匹配结尾，可设定多个关键词，满足其中一个就行，多个关键词用===分隔
还有正则匹配
（2）段落字符数限制  XX字-XX字
（3）段落格式，前面两项匹配上了再处理段落格式和字体格式，否则跳过此段落：
设置段落的对齐方式：（左对齐、居中对齐、右对齐、两端对齐、分散对齐）单选，默认左对齐；
大纲级别（正文文本、1级、2级、3级、4级、5级、6级、7级、8级、）单选，默认正文文本；
对齐方向：（从右向左、从左向右）单选，默认从左向右；
缩进：文本之前XX字符（默认0字符），文本之后XX字符（默认0字符）。特殊格式：无、首行缩进xx字符（默认2字符）、悬挂缩进xx字符（默认2字符 ）， 单选。
间距：行间距的段前距离XX行（默认数值0），段后距离XX行（默认数值0）。行距可选单倍行距、1.5被行距、2被行距、固定值XX磅，单选，默认为1.5被行距。
（4）字体格式：
中文字体类型、中文字体字形（常规、倾斜、加粗、加粗倾斜）单选 默认常规、中文字体字号XX磅，默认12磅。
西文字体类型、西文字体字形（常规、倾斜、加粗、加粗倾斜）单选、西文字体字号XX磅，默认12磅。
复杂文种字体类型、复杂文种字体字形（常规、倾斜、加粗、加粗倾斜）单选、复杂文种字体字号XX磅，默认12磅。
文字字体颜色（颜色板选择，默认字体黑色。）。文字背景颜色（默认无背景色）
注：XXX为用户手动输入内容，首先匹配段落开头关键字或结尾满足其中一个了再进行段落字符数限制，也满足的情况下，再对对应段落进行段落和字体设置。否则跳过改段落。以此类推。

举例：某段落是以“一、”文字开头，匹配上就设置对应段落和文字格式。
6、文档属性设置区域，弹出框设置：标题、主题、作者、管理者、公司、类别、标记、备注 。将用户设定文档元属值性批量应用到文件元属性中。

7、文件名替换区域，弹出框设置，用户可以一个个添加内容替换规则，也可以批量导入文档名替换规则，用于对文档名进行批量替换，可选是否替换文件拓展名

8、页眉页脚设置：弹出框设置，单独设置页眉页脚内容（文字或图片）、删除页眉页脚 。设置与删除功能二选一。
9、删除功能：
测试日志功能按钮改为删除部分内容功能按钮，弹出框设置，以下内容可以单选可以有多选
删除指定图片：删除指定图片、删除指定大小图片、删除所有图片、删除文档结尾处所有图片
删除手机邮箱网址：自动识别并   删除手机号码、删除电话号码、删除邮箱、删除网址、删除超链接（保留文字）
删除包含指定字符区域：删除指定字符所在的段落、删除指定字符所在的文本框、删除指定字符所在的表格   要能输入指定字符，一行一个
删除空白页、段落、行：删除空白页、删除空白段落、删除空白行
删除文档水印

10、功能按钮区域，排列整齐，按钮文字在按钮区域内水平垂直居中。

二、文件处理流程：
1、来源目录加载文件列表，匹配文件后缀.doc .docx,只处理这两个类型文件
2、将文档移动到目标位置
3、进行页面设置
4、进行文件内容替换
5、设置全文段落格式和字体格式
6、逐个寻找符合匹配条件的 文章大标题、文章小标题、一级标题、二级标题、三级标题、四级标题、五级标题、六级标题、七级标题 符合条件的段落设定对应段落的段落格式和字体格式
7、设置文档属性
8、进行文件名替换
三、软件面板不显示实时处理日志，处理日志只保存在软件根目录对应日志文件中，为减少IO占用，日志10秒钟写入一次日志文件。软件面板显示当前处理文件总数、已完成数、失败数。
四、用户所有设置自动保存在软件根目录中的配置文件中，以便于下次打开软件可以直接套用上一次的设置。
五、所有大小功能都为可选功能，根据是否勾选决定是否启用本功能。
六、全程中文交流，每次修改代码前先查看所有项目文件每一行代码，并参考aspose官方文档，了解内容，整体思考，修改完代码后，检查所有文件代码看是否有错误，如果有错误，自动修复错误，确认没问题了自动编译运行。

图片格式：
1、尺寸调整
设置精确宽度/高度
保持或锁定纵横比
百分比缩放
2、图片文本环绕
嵌入式
四周型
紧密型
通过型
上下型
衬于文字下方/上方
3、图片效果
亮度和对比度调整
透明度控制
颜色模式(彩色/灰度/黑白)
图片裁剪
4、边框设置
自定义边框宽度
边框颜色
边框样式(实线/虚线等)
5、位置和对齐
页面内精确定位
水平/垂直对齐
相对于文本/页面/边距的定位
6、旋转和翻转
指定角度旋转
水平/垂直翻转

7、文本替代选项（Alt Text）：
添加描述性文本以增强辅助功能
标题和详细描述
8、高级图片效果：
图片艺术效果（如素描、磨砂玻璃、彩色铅笔等）
3D旋转和透视效果
阴影效果设置（阴影颜色、透明度、角度、距离等）
光泽效果
柔化边缘/羽化效果
9、图片压缩选项：
压缩质量设置
删除裁剪区域选项
图片格式转换
10、超链接设置：
将图片链接到URL、文档内位置或其他文件
11、图片标题和编号：
自动添加编号的图片标题
交叉引用支持
12、锁定比例和锁定位置：
防止图片位置被更改
锁定图片格式
13、水印图片设置：
图片作为水印的特殊配置
14、SVG图片的特殊处理：
SVG特有的大小和格式设置


文档水印功能
1、文本水印
创建自定义文本水印（如"机密"、"草稿"等）
控制文本水印的字体、大小、颜色和样式
设置文本水印的旋转角度（典型的是对角线45度）
调整文本水印的透明度
2、图片水印
从各种图像格式（JPEG、PNG、BMP等）添加图片作为水印，用户指定水印图片位置
控制图片水印的大小、位置和缩放
设置图片水印的透明度
应用图像效果（如灰度、黑白）
3、水印位置和布局
可设置在页面中心或特定位置
可控制水印是否显示在所有页面或特定页面
设置水印在页面的Z顺序（显示在内容前面或后面）
控制水印的空间布局（平铺、单个等）
4、水印格式控制
设置水印边框样式和颜色
调整水印与周围文本的环绕方式
锁定水印防止被编辑或移动
控制水印在打印时是否显示
5、编程控制
通过代码动态添加或移除水印
可基于条件（如文档类型、用户、时间戳等）应用不同水印
支持在文档转换和保存过程中保留水印


1、表格基本属性
表格样式（预设样式、自定义样式）
表格标题
表格宽度（固定宽度、相对宽度、自动宽度）
表格对齐方式（左对齐、居中、右对齐、两端对齐）
表格缩进
允许跨页断行
表格标题行重复（在分页时重复表头）
2、行和列设置
行高（固定高度、至少高度、自动高度）
列宽（固定宽度、按比例宽度、自动宽度）
行列的添加、删除、合并和拆分
表格网格设置（均匀分布、按内容调整）
设置首行、末行、首列或末列特殊格式
3、单元格格式
单元格边框（样式、宽度、颜色）
单元格背景色和底纹
单元格内边距（上、下、左、右）
单元格垂直对齐方式（顶部、居中、底部）
单元格文本方向（水平、垂直）
单元格合并与拆分
4、边框和底纹
表格外边框设置（线型、颜色、宽度）
表格内边框设置（线型、颜色、宽度）
表格底纹和背景颜色
表格边框网格线显示/隐藏
单元格边框线条样式（实线、虚线、点线等）
5、内容格式化
表格内文本对齐方式（左对齐、居中、右对齐）
表格内段落样式和格式
表格内文本间距和行距
单元格内图片大小和位置设置
单元格内项目符号和编号
6、表格布局和定位
表格定位（相对于页面、边距、段落）
文本环绕方式（无环绕、环绕）
表格嵌套（在单元格内创建子表格）
表格上下间距设置
启用/禁用自动调整大小
7、表格行为和条件格式
替代文本（辅助功能）
条件格式（奇偶行/列不同格式）
响应式表格行为（自适应大小）
隐藏/显示特定行或列
8、表格高级特性
表格计算功能（公式、自动计算）
数据绑定功能
表格批量样式应用
表格转换（转为文本、转为图片）
表格锁定保护


软件处理流程是不是这样的
读取线程数-来源目录读取文件-根据线程数将对应数量文件加入处理队列-判断是否是处理源文件-如果不是处理源文件将文件复制到目标位置后进行后续处理-判断是否冲突并进行对应处理-进行页面设置处理-删除内容设置处理-内容替换处理-全局段落格式处理-段落匹配规则处理-页眉页脚设置-文档属性设置-文件名替换-word转pdf  （所有流程根据勾选状态进行，未勾选的跳过该步骤）如果某个步骤运行失败，根据重试次数重试
https://reference.aspose.com/words/net/aspose.words/ https://reference.aspose.com/words/net/aspose.words.ai/ https://reference.aspose.com/words/net/aspose.words.bibliography/ https://reference.aspose.com/words/net/aspose.words.buildingblocks/ https://reference.aspose.com/words/net/aspose.words.comparing/ https://reference.aspose.com/words/net/aspose.words.digitalsignatures/ https://reference.aspose.com/words/net/aspose.words.drawing/ https://reference.aspose.com/words/net/aspose.words.drawing.charts/ https://reference.aspose.com/words/net/aspose.words.drawing.ole/ https://reference.aspose.com/words/net/aspose.words.fields/ https://reference.aspose.com/words/net/aspose.words.fonts/ https://reference.aspose.com/words/net/aspose.words.framesets/ https://reference.aspose.com/words/net/aspose.words.layout/ https://reference.aspose.com/words/net/aspose.words.lists/ https://reference.aspose.com/words/net/aspose.words.loading/ https://reference.aspose.com/words/net/aspose.words.mailmerging/ https://reference.aspose.com/words/net/aspose.words.markup/ https://reference.aspose.com/words/net/aspose.words.math/ https://reference.aspose.com/words/net/aspose.words.notes/ https://reference.aspose.com/words/net/aspose.words.properties/ https://reference.aspose.com/words/net/aspose.words.rendering/ https://reference.aspose.com/words/net/aspose.words.replacing/ https://reference.aspose.com/words/net/aspose.words.reporting/ https://reference.aspose.com/words/net/aspose.words.saving/ https://reference.aspose.com/words/net/aspose.words.settings/ https://reference.aspose.com/words/net/aspose.words.shaping/ https://reference.aspose.com/words/net/aspose.words.tables/ https://reference.aspose.com/words/net/aspose.words.themes/ https://reference.aspose.com/words/net/aspose.words.vba/ https://reference.aspose.com/words/net/aspose.words.webextensions/ 
目前的问题是，在功能弹出框中启用或者取消启用子功能，用户的选择和文本框输入有些没有被保存。你仔细分析所有流程找出问题。
以后全程中文交流，每次修改代码前先查看所有项目文件每一行代码，并参考aspose官方文档，了解内容，整体思考，修改完代码后，检查所有文件代码看是否有错误，如果有错误，自动修复错误，确认没问题了自动编译运行。

帮我修复所有问题

确保DocumentPropertiesForm的SaveSettings()方法处理所有类型控件的值保存
统一表单处理模式，确保所有表单的更改都明确赋值回settings对象
避免在打开表单前重新加载设置
检查并完善每个子控件的事件处理程序，确保所有用户输入都正确更新到相应属性
对于有多个子属性的对象（如DocumentProperties），确保所有子属性都被正确处理@PageSetupForm.cs 


1、批量文档处理：
支持递归处理子目录中的文档
处理进度显示和状态更新
错误处理和重试机制
2、页面设置：
纸张大小和方向
页边距调整
页面背景和边框设置
分栏和页面布局
3、内容替换：
支持普通文本和正则表达式替换
多规则批量处理
可指定替换范围（正文、页眉页脚等）
4、内容删除：
删除图片（全部或基于条件）
删除联系方式（电话、邮箱、URL）
删除特定文本内容
删除空白段落和页面
删除水印和其他格式元素
5、段落格式化：
对齐方式设置
缩进和行距调整
段前段后间距设置
6、段落匹配规则：
基于复杂条件查找特定段落
应用自定义格式
7、页眉页脚处理：
设置页眉页脚内容
配置不同页的页眉页脚显示
8、文档属性设置：
修改标题、作者等文档属性
自定义属性添加与删除
9、文件名处理：
基于规则重命名输出文件
冲突处理策略
10、字体和格式处理：
统一设置字体类型和大小
保留或删除特定格式
11Word转PDF：
可选择将处理后的文档转换为PDF
高级PDF导出选项设置
定时处理：
支持定时自动批量处理文档
多种定时模式（一次性、定时、间隔）

移除主面板的倒计时输入框，这里放一个倒计时设置按钮，弹窗实现以下功能，用户可以在弹窗中设置倒计时选项。
现在我要你增强定时备份功能，目前软件定时备份只有倒计时一种定时备份功能，在此基础上增加功能如下：
1、一次性启动，用户指定一个具体时间  年月日时分秒，到了指定时间启动一次。
2、指定时间启动：可以设置每年、每月、每天、每时的某个具体时间启动。
如果选择了每年启动，则还可以选择启动的月、日、时、分、秒。
如果选择了每月启动，则还可以选择启动的日、时、分、秒。
如果选择了每天启动，则还可以选择启动的时、分、秒。
如果选择了每时启动，则还可以选择启动的分、秒。
3、倒计时启动：用户设定一个具体的第一次启动开始时间  年月日时分秒  ，和间隔时间  XX天XX小时XX分XX秒。到了第一次启动时间后，开始倒计时，每次倒计时结束后启动一次软件备份流程。

4、一个公共区域显示高级设置：此设置对指定时间启动和倒计时启动使用。高级设置用户限定循环次数和结束时机
（1）无限制
（2）运行次数限制：XX次。  用户手动输入循环的次数。
（3）过期时间限制：指定一个到期时间，到了该时间后就停止循环。



删除内容设置弹窗中，添加一个标签页“删除文档”，这个处理流程作为删除内容设置功能的最先处理流程，
该标签页下有以下功能每个子功能要有独立的启用开关：
1、判断文件名长度是否在XX字符至XX字符之间（如果勾选该子功能启用开关，判断当前处理的的文档名称不含拓展名字符长度是否符合，如果符合进行下一步，如果不符合就删除当前文档）
2、判断文档大小是否在XX KB至XX KB之间（如果勾选该子功能启用开关，判断当前处理的文档大小是否符合要求，如果符合进行过下一步处理，如果不符合就删除当前文档。文档大小单位可选b、kb、mb）
3、判断文档内容字符总数是否在XX 字符至 XX 字符之间（如果勾选该子功能开关，判断当前处理的文档内容字符总数是否符合要求，如果符合进行过下一步处理，如果不符合就删除当前文档）
4、判断文档页数是否在 XX页 至XX页之间（如果勾选该子功能开关，判断当前处理文档的页数是否符合要求，如果符合进行过下一步处理，如果不符合就删除当前文档，默认填充2页至200页）
5、判断文档名称是否包含非法词（如果勾选该子功能开关，判断当前处理文档的文件名是否含有某个设定的非法词，如果文件名不含任意一个非法词进行过下一步处理，如果含有非法词就删除当前文档。文件名非法词在对应弹窗中设置，一行一个非法词，此非法词单独存一个配置文件因为非法词可能含特殊字符）
6、判断文档内容是否包含非法词（如果勾选该子功能开关，判断当前处理文档的内容是否含有某个设定的非法词，如果不含任何一个非法词就进行过下一步处理，如果含有非法词就删除当前文档。内容非法词在对应弹窗中设置，一行一个非法词，此非法词单独存一个配置文件因为非法词可能含特殊字符）

如果上面某个流程将文档删除了，那后续流程就全部跳过了
此标签页中每个功能遵循软件主面板的 是否直接处理源文件、复制、移动逻辑

1、立即可以添加的功能（API支持且重要）：
AddSpaceBetweenFarEastAndAlpha/Digit - 中英文间距自动调整
BaselineAlignment - 基线对齐设置
DropCapPosition + LinesToDrop - 首字下沉功能
SpaceAfterAuto/SpaceBeforeAuto - 自动间距控制
SuppressAutoHyphens - 连字符控制
SnapToGrid - 网格对齐
WordWrap - 单词换行控制
2. 中文排版增强功能：
FarEastLineBreakControl - 远东换行规则
HangingPunctuation - 悬挂标点
完善现有的中文版式功能UI
3. 字符单位设置：
CharacterUnit系列属性 - 提供字符为单位的缩进设置选项

参照Aspose.Words的api规范（联网或者看项目目录的Aspose.Words.xml）看看各段落匹配规则弹窗中的各子功能是否已经完全实现并正常可用？或者代码中已有功能但是在UI中缺失。中文交流。


缺失的重要功能 ❌
让我检查并添加这些缺失的功能：
BaselineAlignment - 基线对齐（已在UI中但可能未完全实现）
CharacterUnit系列属性 - 字符单位缩进和间距
LineUnit系列属性 - 行单位间距
MirrorIndents - 镜像缩进
SuppressLineNumbers - 禁止行号




文档网格布局（CharactersPerLine, LinesPerPage, LayoutMode）
多页面布局选项（MultiplePages）
页面边框高级设置
章节和页码高级功能
脚注和尾注选项


缺失或不完整的功能：
1、页面边距标签页：
❌ 页面边距标签页的UI实现不完整（CreateMarginsTab方法被调用但可能未完全实现）
2、网格设置标签页：
❌ 网格设置标签页未在代码中找到
3、布局增强标签页：
❌ 布局增强标签页未在代码中找到
4、视图设置标签页：
❌ 视图设置标签页未在代码中找到
5、重要的Aspose.Words PageSetup属性缺失：
❌ CharactersPerLine - 每行字符数
❌ LinesPerPage - 每页行数
❌ LayoutMode - 布局模式
❌ BorderAlwaysInFront - 边框总在前面
❌ BorderAppliesTo - 边框应用范围
❌ BorderDistanceFrom - 边框距离测量起点
❌ BorderSurroundsHeader/Footer - 边框包围页眉页脚
❌ EndnoteOptions - 尾注选项
❌ FootnoteOptions - 脚注选项


全局段落格式中，图片格式标签页中没勾选的子功能是否会加入处理流程，另外参照Aspose.Words的api规范（联网或者看项目目录的Aspose.Words.xml）看看图片格式各项子功能是否符合api规范，能否达到预期效果


软件主面板中 txt转word、页面设置、删除内容设置、内容替换规则、全局段落格式、段落匹配规则、页眉页脚设置、文档属性、文件名替换、word转pdf   （在勾选框勾选的前提下）这几个大功能的处理顺序时怎样的

帮我更改下执行顺序   从前到后分别执行  txt转word、页面设置、删除内容设置、内容替换规则、全局段落格式、段落匹配规则、页眉页脚设置、文档属性、文件名替换、word转pdf


GlobalParagraphFormatForm.cs  全局段落格式是指应用进文档所有位置？段落格式上面添加一个“应用范围”区域，用于勾选全局段落格式应用范围：正文、页眉、页脚、文本框、脚注、批注
能否实现？


页面设置中各标签页下有很多输入框后面没有对应单位标签，能帮我加上吗？

您提出了一个非常重要的问题！确实，单位的一致性对于用户体验至关重要。让我检查一下当前代码中的单位转换逻辑，确保页面设置中显示的单位与Word中实际显示的单位一致。

参照GlobalParagraphFormatForm.cs全局段落格式弹窗中  段落格式标签页里的基本段落格式和字体格式，对ParagraphPresetRuleForm.cs段落匹配规则里的段落格式、字体格式中的基本字体格式进行升级改造，帮我实现Aspose.Words原生支持单位，具体实现：

一、匹配段落格式弹窗中
1、段落格式标签页下的特殊缩进的单位要下拉框可选单位： 字符、厘米、毫米、磅、英寸。默认用“字符”单位
首行缩进默认2字符
悬挂缩进页默认2字符。
2、行距设定的单位要下拉框可选单位：磅、厘米、毫米、英寸，默认固定值行距28磅
3、行距里的，单倍行距、1.5倍行距、2倍行距、多倍行距  的单位应该是“倍”
单倍行距：输入框显示"1"，右侧显示"倍"
1.5倍行距：输入框显示"1.5"，右侧显示"倍"
2倍行距：输入框显示"2"，右侧显示"倍"
多倍行距：输入框显示"3"，右侧显示"倍"（用户可修改）
最小值：输入框显示"28"，右侧显示单位下拉框（磅、厘米、毫米、英寸）
固定值：输入框显示"28"，右侧显示单位下拉框（磅、厘米、毫米、英寸）

与Microsoft Word的行为完全一致

二、
1、字体格式标签页下各文字字号，应该首选是下拉框可选 初号、小初、一号、小一、一号、小二、二号、小三、三号、小四、四号、小五、五号、小六、六号、小七、七号、小八、八号  这些预设中文字号以及自定义文字大小
2、如果字体格式不用预设字号则要能指定字体以磅为单位的数值，自定义字号的输入框后要显示单位磅。
单位变化注意数值转化准确
说简单点要与word里面的功能一致



现在检查下全局段落格式弹窗中的段落格式标签页下的各单位是否正确转换。再检查单位可以下拉框选择的时候，变更单位时输入框里的数值是否会随单位的变化而变化。

户界面行为：
单倍行距：输入框显示"1"，右侧显示"倍"
1.5倍行距：输入框显示"1.5"，右侧显示"倍"
2倍行距：输入框显示"2"，右侧显示"倍"
多倍行距：输入框显示"3"，右侧显示"倍"（用户可修改）
最小值：输入框显示"28"，右侧显示单位下拉框（磅、厘米、毫米、英寸）
固定值：输入框显示"28"，右侧显示单位下拉框（磅、厘米、毫米、英寸）
单位转换：
倍数行距时，数值直接表示倍数，不进行单位转换
固定值和最小值时，支持不同单位间的数值自动转换
与Microsoft Word的行为完全一致
所有行距设置现在都能正确显示对应的单位，并且支持正确的数值转换，完全符合用户的需求。

参照项目目录的Aspose.Words.xml，检查全局段落格式中段落格式标签页下的子功能有没有逻辑错误

全局段落格式中的颜色设定又不正常了，用户设定了某个颜色后关闭软件，重新打开软件，UI中显示的颜色不是用户设定的颜色了，是不是你配置文件的保存和加载有问题。或者软件初始化顺序有问题。



基于GlobalParagraphFormatForm.cs的实现，我需要对ParagraphMatchRuleForm.cs进行以下升级改造：
一、段落格式标签页改造
1、殊缩进单位选择：添加单位下拉框（字符、厘米、毫米、磅、英寸），默认字符单位，首行缩进和悬挂缩进默认2字符
2、距设置改造：
3、设定单位下拉框（磅、厘米、毫米、英寸），默认固定值28磅
4、距类型显示正确的单位（倍数行距显示"倍"，固定值/最小值显示单位下拉框）
5、持单位间的自动转换
二、字体格式标签页改造
1、文字号预设：添加下拉框选择预设中文字号（初号、小初、一号等）
2、定义字号：支持以磅为单位的自定义字号输入，显示"磅"单位
3、号切换：预设字号和自定义字号之间的智能切换

给DeleteContentForm.cs  删除内容设置弹窗中的 确定、取消按钮左边添加 全选、取消全选按钮，用于对当前查看的标签页中的所有子功能进行一键选择、取消选择


日志设置窗口中，确定、取消按钮要放同一横排。并在这两个按钮的左边添加 全选、取消全选按钮  用于一键对当前页面的子功能进行全选、取消全选。四个按钮放同一水平线横排摆放